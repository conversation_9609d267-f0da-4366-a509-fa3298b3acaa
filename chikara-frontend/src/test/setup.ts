import { cleanup } from "@testing-library/react";
import { afterEach, beforeEach, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import React from "react";

// Global mocks for environment variables
vi.mock("import.meta", () => ({
    env: {
        VITE_IMAGE_CDN_URL: "https://cloudflare-image.jamessut.workers.dev",
        VITE_API_URL: "http://localhost:3000",
        NODE_ENV: "test",
        MODE: "test",
    },
}));

// Global React DOM mocks
vi.mock("react-dom/client", () => ({
    default: {
        createRoot: vi.fn(() => ({
            render: vi.fn(),
        })),
    },
    createRoot: vi.fn(() => ({
        render: vi.fn(),
    })),
}));

// Global React Router mocks
vi.mock("@/app/Routes/AppRouter", () => ({
    default: () => React.createElement("div", { "data-testid": "app-router" }, "App Router"),
}));

// Global React Query mocks
vi.mock("@tanstack/react-query", async (importOriginal) => {
    const actual = await importOriginal<typeof import("@tanstack/react-query")>();
    return {
        ...actual,
        QueryClientProvider: ({ children }: { children: React.ReactNode }) =>
            React.createElement("div", { "data-testid": "query-client-provider" }, children),
    };
});

vi.mock("@tanstack/react-query-devtools", () => ({
    ReactQueryDevtools: () =>
        React.createElement("div", { "data-testid": "react-query-devtools" }, "React Query Devtools"),
}));

// Global component mocks
vi.mock("@/components/Layout/ErrorBoundary", () => ({
    default: ({ children }: { children: React.ReactNode }) =>
        React.createElement("div", { "data-testid": "error-boundary" }, children),
}));

vi.mock("@/components/ServiceWorker/ServiceWorkerUpdater", () => ({
    ServiceWorkerUpdater: () =>
        React.createElement("div", { "data-testid": "service-worker-updater" }, "Service Worker Updater"),
}));

// Global library mocks
vi.mock("@/lib/analytics", () => ({
    initializeAnalytics: vi.fn(),
}));

vi.mock("@/lib/queryClient", () => ({
    createQueryClient: vi.fn(() => ({
        defaultOptions: {
            queries: {
                retry: false,
            },
        },
    })),
}));

// Global DOM API mocks
const mockAddEventListener = vi.fn();
const mockReload = vi.fn();

// Mock window.addEventListener
Object.defineProperty(window, "addEventListener", {
    value: mockAddEventListener,
    writable: true,
});

// Mock location.reload
delete (window as any).location;
(window as any).location = { reload: mockReload };

// Mock document.getElementById
const mockGetElementById = vi.fn(() => ({
    id: "root",
}));

Object.defineProperty(document, "getElementById", {
    value: mockGetElementById,
    writable: true,
});

// Mock Date for consistent testing
const mockDate = vi.fn();
const originalDate = Date;

beforeEach(() => {
    // Reset Date mock before each test
    vi.setSystemTime(new Date("2024-01-15T12:00:00Z"));
    // Clear all mocks before each test
    vi.clearAllMocks();
});

afterEach(() => {
    // Clean up after each test
    cleanup();
    vi.useRealTimers();
    // Restore all mocks after each test
    vi.restoreAllMocks();
});

// Global DOM mocks
Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
    })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
}));

// Mock console methods to reduce noise in tests
const originalConsoleWarn = console.warn;
const originalConsoleError = console.error;

beforeEach(() => {
    console.warn = vi.fn();
    console.error = vi.fn();
});

afterEach(() => {
    console.warn = originalConsoleWarn;
    console.error = originalConsoleError;
});

// Mock localStorage
const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
    value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
};
Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock URL.createObjectURL
global.URL.createObjectURL = vi.fn();
global.URL.revokeObjectURL = vi.fn();

// Mock Audio
const mockAudioInstance = {
    play: vi.fn(),
    pause: vi.fn(),
    load: vi.fn(),
};

global.Audio = vi.fn(() => mockAudioInstance);

// Mock Image
global.Image = vi.fn().mockImplementation(() => ({
    onload: vi.fn(),
    onerror: vi.fn(),
    src: "",
    alt: "",
}));

// Mock HTMLCanvasElement
HTMLCanvasElement.prototype.getContext = vi.fn();

// Mock scrollTo
window.scrollTo = vi.fn();

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => setTimeout(cb, 0));
global.cancelAnimationFrame = vi.fn();

// Mock performance
Object.defineProperty(window, "performance", {
    value: {
        now: vi.fn(() => Date.now()),
        mark: vi.fn(),
        measure: vi.fn(),
        getEntriesByName: vi.fn(() => []),
        getEntriesByType: vi.fn(() => []),
    },
    writable: true,
});

// Mock crypto
Object.defineProperty(window, "crypto", {
    value: {
        randomUUID: vi.fn(() => "mock-uuid"),
        getRandomValues: vi.fn((arr) => arr),
    },
    writable: true,
});

// Export commonly used mock functions for test access
export const mockFunctions = {
    addEventListener: mockAddEventListener,
    reload: mockReload,
    getElementById: mockGetElementById,
    audioInstance: mockAudioInstance,
};
